package pipe

import (
	"fmt"
	"io"
	"net"
	"server/core/listener/stats"
	"server/core/manager/cache"
	"server/core/manager/clientmgr"
	"server/core/manager/workerpool"
	"server/global"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PipeListener 表示一个pipe类型的监听器
type PipeListener struct {
	ID                uint
	LocalListenAddr   string
	RemoteConnectAddr string
	Status            int
	Listener          net.Listener
	Connections       map[string]net.Conn
	Mutex             sync.Mutex

	// 统计信息
	stats struct {
		startTime         time.Time
		totalConnections  int64
		activeConnections int64
		dataTransferred   int64
		packetsProcessed  int64
		errorCount        int64
		lastActivity      time.Time
		bytesReceived     int64
		bytesSent         int64
		packetsReceived   int64
		packetsSent       int64
		mutex             sync.RWMutex
	}
}

// handleConnections 处理监听器的连接
func (l *PipeListener) handleConnections() {
	for {
		// 接受新连接
		conn, err := l.Listener.Accept()
		if err != nil {
			// 如果监听器已关闭，退出循环
			if opErr, ok := err.(*net.OpError); ok && opErr.Err.Error() == "use of closed network connection" {
				break
			}
			global.LOG.Error(fmt.Sprintf("接受连接失败: %s", err.Error()))
			continue
		}
		// 🚀 使用网络工作池处理新连接，避免无限制创建goroutine
		connCopy := conn // 避免闭包问题
		task := workerpool.NewNetworkTask("pipe_handle_connection", func() error {
			l.handleConnection(connCopy)
			return nil
		})

		if err := workerpool.SubmitNetworkTask(task); err != nil {
			global.LOG.Error("提交Pipe连接处理任务失败",
				zap.String("remoteAddr", conn.RemoteAddr().String()),
				zap.Error(err))
			conn.Close() // 如果无法提交任务，关闭连接
		}
	}
}

// handleConnection 处理单个连接
func (l *PipeListener) handleConnection(conn net.Conn) {
	remoteAddr := conn.RemoteAddr().String()

	// 增加连接统计
	l.incrementConnection()
	defer l.decrementConnection()

	// 添加到连接列表
	l.Mutex.Lock()
	l.Connections[remoteAddr] = conn
	l.Mutex.Unlock()

	// 记录连接信息
	global.LOG.Info(fmt.Sprintf("接收到来自 %s 的shell连接到pipe监听器 %s", remoteAddr, l.LocalListenAddr))

	// 默认操作系统类型，后续通过数据分析动态调整
	os := "unix" // 大多数反弹shell来自Unix-like系统
	detectedOS := false

	// 注册客户端
	client, err := clientmgr.GlobalClientManager.RegisterClient(l.ID, "pipe", remoteAddr, os, "interactive")
	if err != nil {
		global.LOG.Error("注册客户端失败", zap.Error(err))
		return
	}
	global.LOG.Info(fmt.Sprintf("客户端注册成功，ID: %d", client.ID))

	conn.Write([]byte("\r\n"))

	// 直接在主goroutine中处理数据读取，避免不必要的goroutine
	buf := make([]byte, 4096)
	for {
		n, err := conn.Read(buf)
		if err != nil {
			if err == io.EOF {
				global.LOG.Info(fmt.Sprintf("连接 %s 已关闭", remoteAddr))
			} else {
				global.LOG.Error(fmt.Sprintf("读取数据失败: %s", err.Error()))
			}
			break
		}

		if n > 0 {
			data := string(buf[:n])

			// 添加接收数据统计
			l.addBytesReceived(int64(n))
			l.addDataTransferred(n)

			// 动态检测操作系统（仅在首次检测时）
			if !detectedOS {
				detectedOS = true
				dataLower := strings.ToLower(data)
				// 检测Windows系统
				if strings.Contains(dataLower, "c:\\") ||
					strings.Contains(dataLower, "windows") ||
					strings.Contains(dataLower, "cmd") ||
					strings.Contains(dataLower, "powershell") ||
					strings.Contains(dataLower, "ps") {
					os = "windows"
					global.LOG.Info(fmt.Sprintf("检测到Windows系统，客户端ID: %d", client.ID))
				} else if strings.Contains(dataLower, "support.apple.com") {
					os = "darwin"
					global.LOG.Info(fmt.Sprintf("检测到macOS系统，客户端ID: %d", client.ID))
				} else if strings.Contains(dataLower, "linux") {
					os = "linux"
					global.LOG.Info(fmt.Sprintf("检测到Linux系统，客户端ID: %d", client.ID))
				}

				// 更新客户端操作系统信息
				if err := clientmgr.GlobalClientManager.UpdateClientOS(client.ID, os); err != nil {
					global.LOG.Error("更新客户端操作系统信息失败", zap.Error(err))
				}
			}

			// 记录接收到的数据
			global.LOG.Info(fmt.Sprintf("从 %s 接收到数据: %s", remoteAddr, strings.TrimSpace(data)))

			// 更新客户端最后活动时间
			if client.ID > 0 {
				if err := clientmgr.GlobalClientManager.UpdateClientStatus(client.ID, 1); err != nil {
					global.LOG.Error("更新客户端状态失败", zap.Error(err))
				}

				// 🚀 新方式：存储命令输出到ResponseManager
				global.LOG.Info(fmt.Sprintf("存储命令输出到ResponseManager，客户端ID: %d", client.ID))
				cache.ResponseMgr.StoreCommandOutput(client.ID, data, "command_output")
			}
		}
	}

	// 连接结束，从列表中移除
	l.Mutex.Lock()
	delete(l.Connections, remoteAddr)
	conn.Close()
	l.Mutex.Unlock()

	// 更新客户端状态为离线
	if client.ID > 0 {
		if err := clientmgr.GlobalClientManager.UpdateClientStatus(client.ID, 0); err != nil {
			global.LOG.Error("更新客户端状态失败", zap.Error(err))
		}
	}
}

// SendCommand 向指定连接发送命令
func (l *PipeListener) SendCommand(remoteAddr string, command string) error {
	l.Mutex.Lock()
	defer l.Mutex.Unlock()

	// 检查连接是否存在
	conn, exists := l.Connections[remoteAddr]
	if !exists {
		return fmt.Errorf("连接 %s 不存在", remoteAddr)
	}

	// 发送命令
	n, err := conn.Write([]byte(command))
	if err != nil {
		l.incrementError()
		return fmt.Errorf("发送命令失败: %s", err.Error())
	}

	// 添加发送数据统计
	l.addBytesSent(int64(n))

	return nil
}

// 统计方法

// initStats 初始化统计信息
func (l *PipeListener) initStats() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.startTime = time.Now()
	l.stats.lastActivity = time.Now()
}

// incrementConnection 增加连接计数
func (l *PipeListener) incrementConnection() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.totalConnections++
	l.stats.activeConnections++
	l.stats.lastActivity = time.Now()
}

// decrementConnection 减少连接计数
func (l *PipeListener) decrementConnection() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	if l.stats.activeConnections > 0 {
		l.stats.activeConnections--
	}
	l.stats.lastActivity = time.Now()
}

// addDataTransferred 添加传输数据量（基于数据长度）
func (l *PipeListener) addDataTransferred(dataLength int) {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.dataTransferred += int64(dataLength)
	l.stats.lastActivity = time.Now()
}

// addBytesReceived 添加接收字节数
func (l *PipeListener) addBytesReceived(bytes int64) {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.bytesReceived += bytes
	l.stats.packetsReceived++
	l.stats.packetsProcessed++
	l.stats.lastActivity = time.Now()
}

// addBytesSent 添加发送字节数
func (l *PipeListener) addBytesSent(bytes int64) {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.bytesSent += bytes
	l.stats.packetsSent++
	l.stats.lastActivity = time.Now()
}

// incrementError 增加错误计数
func (l *PipeListener) incrementError() {
	l.stats.mutex.Lock()
	defer l.stats.mutex.Unlock()
	l.stats.errorCount++
	l.stats.lastActivity = time.Now()
}

// getStats 获取统计信息
func (l *PipeListener) getStats() *stats.BasicListenerStats {
	l.stats.mutex.RLock()
	defer l.stats.mutex.RUnlock()

	return &stats.BasicListenerStats{
		ListenerID:        l.ID,
		ListenerType:      "pipe",
		Address:           l.LocalListenAddr,
		Active:            l.Status == 1,
		StartTime:         l.stats.startTime,
		TotalConnections:  l.stats.totalConnections,
		ActiveConnections: l.stats.activeConnections,
		DataTransferred:   l.stats.dataTransferred,
		PacketsProcessed:  l.stats.packetsProcessed,
		ErrorCount:        l.stats.errorCount,
		LastActivity:      l.stats.lastActivity,
	}
}
