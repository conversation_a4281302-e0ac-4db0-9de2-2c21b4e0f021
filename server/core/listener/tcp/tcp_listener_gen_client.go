package tcp

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"server/global"
	"server/utils"
	"strings"
)

// generateLinuxClient 生成Linux客户端，支持多架构交叉编译
func (l *TCPListener) generateLinuxClient() error {
	// 准备临时目录
	tempDir, err := os.MkdirTemp("", "client_build_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %s", err.Error())
	}
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 准备客户端代码
	if err := l.prepareLinuxClientCode(tempDir); err != nil {
		return err
	}

	// 编译不同架构的客户端（包括headless版本）
	architectures := []struct {
		goos   string
		goarch string
		key    string // 客户端映射的键名
		tags   string // build tags
		suffix string // 文件名后缀
	}{
		{"linux", "amd64", "l64", "", ""},                   // Linux x86_64 (X11版本)
		{"linux", "386", "l32", "", ""},                     // Linux x86 (X11版本)
		{"linux", "arm64", "a64", "", ""},                   // Linux ARM64 (X11版本)
		{"linux", "arm", "a32", "", ""},                     // Linux ARM (X11版本)
		{"linux", "amd64", "l64h", "headless", "_headless"}, // Linux x86_64 (headless版本)
		{"linux", "386", "l32h", "headless", "_headless"},   // Linux x86 (headless版本)
		{"linux", "arm64", "a64h", "headless", "_headless"}, // Linux ARM64 (headless版本)
		{"linux", "arm", "a32h", "headless", "_headless"},   // Linux ARM (headless版本)
	}

	for _, arch := range architectures {
		clientBin, err := l.compileClientWithTags(tempDir, arch.goos, arch.goarch, arch.tags)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("编译 %s/%s%s 客户端失败: %s", arch.goos, arch.goarch, arch.suffix, err.Error()))
			continue
		}

		// 生成文件名
		fileName := fmt.Sprintf("%s_%s_%s%s", l.ClientPrefix, arch.goos, arch.goarch, arch.suffix)

		// 保存到文件系统
		filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)
		if err := os.WriteFile(filePath, clientBin, 0755); err != nil {
			global.LOG.Error(fmt.Sprintf("保存 %s/%s%s 客户端到文件系统失败: %s", arch.goos, arch.goarch, arch.suffix, err.Error()))
			continue
		}

		// 保存文件名到客户端映射
		l.Mutex.Lock()
		l.Clients[arch.key] = fileName
		l.Mutex.Unlock()
		global.LOG.Info(fmt.Sprintf("成功生成 %s/%s%s 客户端，保存到: %s，大小: %d 字节", arch.goos, arch.goarch, arch.suffix, filePath, len(clientBin)))
	}

	// 检查是否至少有一个客户端编译成功
	if len(l.Clients) == 0 {
		return fmt.Errorf("所有架构的客户端编译均失败")
	}

	return nil
}

// prepareLinuxClientCode 准备客户端代码和依赖
func (l *TCPListener) prepareLinuxClientCode(tempDir string) error {
	// 从嵌入的template中提取完整的Linux客户端代码
	if err := extractClientCodeToTemp("linux", tempDir); err != nil {
		return fmt.Errorf("提取Linux客户端代码失败: %v", err)
	}

	// 准备配置数据用于替换
	serverAddr := l.RemoteConnectAddr
	publickey, _ := utils.EncodePublicKeyToPEM(l.rsaPublicKey)

	// 更新所有.go文件中的服务器地址和公钥
	if err := l.updateClientConfig(tempDir, serverAddr, publickey); err != nil {
		return fmt.Errorf("更新客户端配置失败: %v", err)
	}

	// 创建go.mod文件
	if err := l.createGoMod(tempDir, "linux"); err != nil {
		return fmt.Errorf("创建go.mod失败: %v", err)
	}

	return nil
}

// updateClientConfig 更新客户端配置文件中的服务器地址和公钥
func (l *TCPListener) updateClientConfig(tempDir, serverAddr, publicKey string) error {
	// 递归遍历所有.go文件，替换占位符
	return filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.go文件
		if !strings.HasSuffix(path, ".go") {
			return nil
		}

		// 读取文件
		content, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("读取文件 %s 失败: %v", path, err)
		}

		// 替换配置占位符
		contentStr := string(content)
		originalContent := contentStr
		contentStr = strings.ReplaceAll(contentStr, "{{.ServerAddr}}", serverAddr)
		contentStr = strings.ReplaceAll(contentStr, "{{.PublicKey}}", publicKey)

		// 只有内容发生变化时才写回文件
		if contentStr != originalContent {
			if err := os.WriteFile(path, []byte(contentStr), 0644); err != nil {
				return fmt.Errorf("写入文件 %s 失败: %v", path, err)
			}
		}

		return nil
	})
}

// generateWindowsClient 生成Windows客户端，支持多架构交叉编译
func (l *TCPListener) generateWindowsClient() error {
	// 准备临时目录
	tempDir, err := os.MkdirTemp("", "client_build_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %s", err.Error())
	}
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 准备客户端代码
	if err := l.prepareWindowsClientCode(tempDir); err != nil {
		return err
	}

	// 编译不同架构的客户端
	architectures := []struct {
		goos   string
		goarch string
		key    string // 客户端映射的键名
	}{
		{"windows", "amd64", "wl64"}, // Windows x86_64
		{"windows", "386", "wl32"},   // Windows x86
		{"windows", "arm64", "wa64"}, // Windows ARM64
		{"windows", "arm", "wa32"},   // Windows ARM
	}

	for _, arch := range architectures {
		clientBin, err := l.compileClient(tempDir, arch.goos, arch.goarch)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("编译 %s/%s 客户端失败: %s", arch.goos, arch.goarch, err.Error()))
			continue
		}

		// 生成文件名
		fileName := fmt.Sprintf("%s_%s_%s.exe", l.ClientPrefix, arch.goos, arch.goarch)

		// 保存到文件系统
		filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)
		if err := os.WriteFile(filePath, clientBin, 0755); err != nil {
			global.LOG.Error(fmt.Sprintf("保存 %s/%s 客户端到文件系统失败: %s", arch.goos, arch.goarch, err.Error()))
			continue
		}

		// 保存文件名到客户端映射
		l.Mutex.Lock()
		l.Clients[arch.key] = fileName
		l.Mutex.Unlock()
		global.LOG.Info(fmt.Sprintf("成功生成 %s/%s 客户端，保存到: %s，大小: %d 字节", arch.goos, arch.goarch, filePath, len(clientBin)))
	}

	// 检查是否至少有一个客户端编译成功
	if len(l.Clients) == 0 {
		return fmt.Errorf("所有架构的客户端编译均失败")
	}

	return nil
}

// prepareWindowsClientCode 准备客户端代码和依赖
func (l *TCPListener) prepareWindowsClientCode(tempDir string) error {
	// 从嵌入的template中提取完整的Windows客户端代码
	if err := extractClientCodeToTemp("windows", tempDir); err != nil {
		return fmt.Errorf("提取Windows客户端代码失败: %v", err)
	}

	// 准备配置数据用于替换
	serverAddr := l.RemoteConnectAddr
	publickey, _ := utils.EncodePublicKeyToPEM(l.rsaPublicKey)

	// 更新所有.go文件中的服务器地址和公钥
	if err := l.updateClientConfig(tempDir, serverAddr, publickey); err != nil {
		return fmt.Errorf("更新客户端配置失败: %v", err)
	}

	// 创建go.mod文件
	if err := l.createGoMod(tempDir, "windows"); err != nil {
		return fmt.Errorf("创建go.mod失败: %v", err)
	}

	return nil
}

// generateDarwinClient 生成Darwin客户端，支持多架构交叉编译
func (l *TCPListener) generateDarwinClient() error {
	// 准备临时目录
	tempDir, err := os.MkdirTemp("", "client_build_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %s", err.Error())
	}
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 准备客户端代码
	if err := l.prepareDarwinClientCode(tempDir); err != nil {
		return err
	}

	// 编译不同架构的客户端
	architectures := []struct {
		goos   string
		goarch string
		key    string // 客户端映射的键名
	}{
		{"darwin", "amd64", "d64"},  // Darwin x86_64
		{"darwin", "arm64", "da64"}, // Darwin ARM64 (Apple Silicon)
	}

	for _, arch := range architectures {
		clientBin, err := l.compileClient(tempDir, arch.goos, arch.goarch)
		if err != nil {
			global.LOG.Error(fmt.Sprintf("编译 %s/%s 客户端失败: %s", arch.goos, arch.goarch, err.Error()))
			continue
		}

		// 生成文件名
		fileName := fmt.Sprintf("%s_%s_%s", l.ClientPrefix, arch.goos, arch.goarch)

		// 保存到文件系统
		filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)
		if err := os.WriteFile(filePath, clientBin, 0755); err != nil {
			global.LOG.Error(fmt.Sprintf("保存 %s/%s 客户端到文件系统失败: %s", arch.goos, arch.goarch, err.Error()))
			continue
		}

		// 保存文件名到客户端映射
		l.Mutex.Lock()
		l.Clients[arch.key] = fileName
		l.Mutex.Unlock()
		global.LOG.Info(fmt.Sprintf("成功生成 %s/%s 客户端，保存到: %s，大小: %d 字节", arch.goos, arch.goarch, filePath, len(clientBin)))
	}

	return nil
}

// prepareDarwinClientCode 准备Darwin客户端代码和依赖
func (l *TCPListener) prepareDarwinClientCode(tempDir string) error {
	// 从嵌入的template中提取完整的Darwin客户端代码
	if err := extractClientCodeToTemp("darwin", tempDir); err != nil {
		return fmt.Errorf("提取Darwin客户端代码失败: %v", err)
	}

	// 准备配置数据用于替换
	serverAddr := l.RemoteConnectAddr
	publickey, _ := utils.EncodePublicKeyToPEM(l.rsaPublicKey)

	// 更新所有.go文件中的服务器地址和公钥
	if err := l.updateClientConfig(tempDir, serverAddr, publickey); err != nil {
		return fmt.Errorf("更新客户端配置失败: %v", err)
	}

	// 创建go.mod文件
	if err := l.createGoMod(tempDir, "darwin"); err != nil {
		return fmt.Errorf("创建go.mod失败: %v", err)
	}

	return nil
}

// setupCrossCompilerEnv 设置交叉编译环境变量（仅在Windows下生效）
func setupCrossCompilerEnv(env []string, goos, goarch string) []string {
	// 只在Windows主机上进行智能编译器选择
	if runtime.GOOS != "windows" {
		return env
	}

	// 检查是否为交叉编译
	isCrossCompile := goos != runtime.GOOS

	if isCrossCompile {
		switch goos {
		case "darwin":
			// macOS 交叉编译：由于缺少系统框架，禁用 CGO
			// macOS 的 CoreFoundation、IOKit 等框架无法在 Windows 上交叉编译
			env = append(env, "CGO_ENABLED=0")
		case "linux":
			// Linux 交叉编译：尝试使用 Zig，但对于需要 X11 的代码禁用 CGO
			env = append(env, "CGO_ENABLED=0")
			// 注释：如果需要启用 CGO，需要安装对应的开发库
			// 例如：sudo apt-get install libx11-dev libjpeg-dev libpng-dev
		default:
			// 其他平台交叉编译时禁用 CGO
			env = append(env, "CGO_ENABLED=0")
		}
		return env
	}

	// 本地编译时显式启用 CGO，使用默认编译器
	switch goos {
	case "windows":
		// Windows 本地编译，显式启用 CGO 并使用默认编译器（GCC/MinGW）
		env = append(env, "CGO_ENABLED=1")
		// 不设置 CC/CXX，让 Go 使用系统默认编译器
	}

	return env
}

// compileClient 编译指定架构的客户端
func (l *TCPListener) compileClient(tempDir, goos, goarch string) ([]byte, error) {
	// 设置输出文件名
	outputName := fmt.Sprintf("client_%s_%s", goos, goarch)

	// 编译客户端
	cmd := exec.Command("go", "build", "-o", outputName)
	cmd.Dir = tempDir

	// 设置基础环境变量
	env := append(os.Environ(),
		fmt.Sprintf("GOOS=%s", goos),
		fmt.Sprintf("GOARCH=%s", goarch),
	)

	// 在Windows下设置智能交叉编译器
	env = setupCrossCompilerEnv(env, goos, goarch)
	cmd.Env = env

	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("编译失败: %s, 输出: %s", err.Error(), string(output))
	}

	// 读取编译后的二进制文件
	clientBinPath := filepath.Join(tempDir, outputName)
	clientBin, err := os.ReadFile(clientBinPath)
	if err != nil {
		return nil, fmt.Errorf("读取编译后的客户端失败: %s", err.Error())
	}

	return clientBin, nil
}

// compileClientWithTags 编译指定架构和build tags的客户端
func (l *TCPListener) compileClientWithTags(tempDir, goos, goarch, tags string) ([]byte, error) {
	// 设置输出文件名
	outputName := fmt.Sprintf("client_%s_%s", goos, goarch)
	if tags != "" {
		outputName += "_" + tags
	}

	// 构建编译命令
	args := []string{"build", "-o", outputName}
	if tags != "" {
		args = append(args, "-tags", tags)
	}

	// 编译客户端
	cmd := exec.Command("go", args...)
	cmd.Dir = tempDir

	// 设置基础环境变量
	env := append(os.Environ(),
		fmt.Sprintf("GOOS=%s", goos),
		fmt.Sprintf("GOARCH=%s", goarch),
	)

	// 在Windows下设置智能交叉编译器
	env = setupCrossCompilerEnv(env, goos, goarch)
	cmd.Env = env

	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("编译失败: %s, 输出: %s", err.Error(), string(output))
	}

	// 读取编译后的二进制文件
	clientBinPath := filepath.Join(tempDir, outputName)
	clientBin, err := os.ReadFile(clientBinPath)
	if err != nil {
		return nil, fmt.Errorf("读取编译后的客户端失败: %s", err.Error())
	}

	return clientBin, nil
}

// createGoMod 创建go.mod文件
func (l *TCPListener) createGoMod(tempDir, moduleName string) error {
	goModContent := fmt.Sprintf(`module %s

go 1.24

require (
	github.com/creack/pty v1.1.24
	github.com/google/uuid v1.6.0
	github.com/minio/sha256-simd v1.0.1
	github.com/shirou/gopsutil/v3 v3.24.5
	golang.org/x/sys v0.34.0
	golang.org/x/term v0.32.0
)
`, moduleName)

	goModFile := filepath.Join(tempDir, "go.mod")
	if err := os.WriteFile(goModFile, []byte(goModContent), 0644); err != nil {
		return fmt.Errorf("写入go.mod失败: %v", err)
	}

	// 运行go mod tidy下载依赖并生成go.sum
	cmd := exec.Command("go", "mod", "tidy")
	cmd.Dir = tempDir
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("执行go mod tidy失败: %v, 输出: %s", err, string(output))
	}

	return nil
}
